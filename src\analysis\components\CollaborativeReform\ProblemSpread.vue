<template>
  <div class="barbox">
    <div ref="bar" class="barbox" />
    <div class="legend">
      <div class="legend-item legend-item-1">资讯类：<span>{{ twoPieData.find(item => item.name === '咨询类')?.value }}</span></div>
      <div class="legend-item legend-item-2">协调类：<span>{{ twoPieData.find(item => item.name === '协调类')?.value }}</span></div>
      <div class="legend-item legend-item-3">审批类：<span>{{ twoPieData.find(item => item.name === '审批类')?.value }}</span></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'

export default {
  name: 'ProblemSpread',
  props: {
    spreadList: {
      type: Array,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      screenWidth: window.screen.width, // 屏幕宽度
      myChart: null,
      onePieData: [],
      twoPieData: []
    }
  },
  mounted () {
    this.$nextTick(function () {
      this.opreatData()
    })
  },
  destroyed () {
    window.removeEventListener('resize', this.resizeChart)
    if (this.myChart) {
      this.myChart.dispose()
    }
  },
  methods: {
    opreatData() {
      const arr = this.spreadList
      arr.forEach(item => {
        if (item.name === '未解决') {
          this.onePieData.push({ name: item.name, value: item.value, itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 0,
              colorStops: [
                { offset: 0, color: '#66A1FF' },
                { offset: 1, color: '#3381FF' }
              ]
            }
          }})
        } else if (item.name === '已解决') {
          this.onePieData.push({ name: item.name, value: item.value, itemStyle: { color: {
            type: 'linear',
            x: 0, y: 0, x2: 1, y2: 0,
            colorStops: [
              { offset: 0, color: '#F7BD7E' },
              { offset: 1, color: '#F7A54D' }
            ]
          }}})
        } else if (item.name === '总数') {
          item.list.forEach(i => {
            if (i.name === '咨询类') {
              this.twoPieData.push({ name: i.name, value: i.value, itemStyle: { color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 0,
                colorStops: [
                  { offset: 0, color: '#82CFFF' },
                  { offset: 1, color: '#50BBFF' }
                ]
              }}})
            } else if (i.name === '协调类') {
              this.twoPieData.push({ name: i.name, value: i.value, itemStyle: { color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 0,
                colorStops: [
                  { offset: 0, color: '#66A1FF' },
                  { offset: 1, color: '#3381FF' }
                ]
              }}})
            } else if (i.name === '审批类') {
              this.twoPieData.push({ name: i.name, value: i.value, itemStyle: { color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 0,
                colorStops: [
                  { offset: 0, color: '#F7BD7E' },
                  { offset: 1, color: '#F7A54D' }
                ]
              }}})
            }
          })
        }
      })
      this.init()
      this.resizeChart()
    },
    resizeChart () {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    nowSize (val, initWidth = 1920) {
      const nowClientWidth = document.documentElement.clientWidth
      return val * (nowClientWidth / initWidth)
    },
    init () {
      // 只初始化一次
      if (!this.myChart) {
        this.myChart = echarts.init(this.$refs.bar, null, { devicePixelRatio: 2.5 })
        this.myChart.on('click', (params) => {
          if (params.seriesName === 'PieAll') {
            const flag = params.name
            this.spreadList.forEach(item => {
              if (item.name === flag) {
                this.twoPieData = []
                const colorArr = [{
                  type: 'linear',
                  x: 0, y: 0, x2: 1, y2: 0,
                  colorStops: [
                    { offset: 0, color: '#82CFFF' },
                    { offset: 1, color: '#50BBFF' }
                  ]
                }, {
                  type: 'linear',
                  x: 0, y: 0, x2: 1, y2: 0,
                  colorStops: [
                    { offset: 0, color: '#66A1FF' },
                    { offset: 1, color: '#3381FF' }
                  ]
                }, {
                  type: 'linear',
                  x: 0, y: 0, x2: 1, y2: 0,
                  colorStops: [
                    { offset: 0, color: '#F7BD7E' },
                    { offset: 1, color: '#F7A54D' }
                  ]
                }]
                item.list.forEach((e, i) => {
                  this.twoPieData.push({ name: e.name, value: e.value, itemStyle: { color: colorArr[i] }})
                })
              }
            })
            // 只更新 option，不再重新 init
            this.updateOption(params.name)
          }
        })
        this.myChart.getZr().on('mousemove', () => {
          this.myChart.getZr().setCursorStyle('default')
        })
        window.addEventListener('resize', this.resizeChart)
      }
      this.updateOption()
    },
    updateOption(paramsName = '已解决') {
      // 检查是否只有一个数据项且占100%
      const hasOnlyOneItem = this.onePieData.length === 1
      const totalValue = this.onePieData.reduce((sum, item) => sum + item.value, 0)
      const isSingleFullData = hasOnlyOneItem || (this.onePieData.length > 0 && this.onePieData.some(item => item.value === totalValue))

      this.option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}'
        },
        series: [
          {
            name: 'PieAll',
            center: ['20%', '50%'],
            padAngle: isSingleFullData ? 0 : 2, // 如果是单一数据占100%，不设置间隔
            label: {
              position: 'inner',
              fontSize: 14,
              fontWeight: '600',
              formatter: '{b}\n{c}',
              color: '#ffffff'
            },
            type: 'pie',
            radius: '80%',
            data: this.onePieData,
            itemStyle: {
              borderWidth: isSingleFullData ? 0 : 2, // 如果是单一数据占100%，不设置边框
              borderColor: isSingleFullData ? 'transparent' : 'rgba(255, 255, 255, 0)'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          },
          {
            name: 'PieHalf',
            type: 'pie',
            padAngle: (() => {
              // 检查第二个饼图是否只有一个数据项且占100%
              const hasOnlyOneItem = this.twoPieData.length === 1
              const totalValue = this.twoPieData.reduce((sum, item) => sum + item.value, 0)
              const isSingleFullData = hasOnlyOneItem || (this.twoPieData.length > 0 && this.twoPieData.some(item => item.value === totalValue))
              return isSingleFullData ? 0 : 2
            })(),
            radius: ['70%', '90%'], // 扇形厚度
            center: ['30%', '50%'], // 更靠右
            startAngle: 60,
            endAngle: -60,
            itemStyle: {
              borderWidth: (() => {
                // 检查第二个饼图是否只有一个数据项且占100%
                const hasOnlyOneItem = this.twoPieData.length === 1
                const totalValue = this.twoPieData.reduce((sum, item) => sum + item.value, 0)
                const isSingleFullData = hasOnlyOneItem || (this.twoPieData.length > 0 && this.twoPieData.some(item => item.value === totalValue))
                return isSingleFullData ? 0 : 2
              })(),
              borderColor: (() => {
                // 检查第二个饼图是否只有一个数据项且占100%
                const hasOnlyOneItem = this.twoPieData.length === 1
                const totalValue = this.twoPieData.reduce((sum, item) => sum + item.value, 0)
                const isSingleFullData = hasOnlyOneItem || (this.twoPieData.length > 0 && this.twoPieData.some(item => item.value === totalValue))
                return isSingleFullData ? 'transparent' : 'rgba(255, 255, 255, 0)'
              })()
            },
            label: {
              show: false,
              position: 'inner',
              fontSize: 14,
              fontWeight: '600',
              formatter: '{b}\n{c}',
              color: '#ffffff'
            },
            data: this.twoPieData
            // itemStyle: {
            //   borderWidth: 2,
            //   borderColor: '#0b246b'
            // }
            // markLine: {
            //   symbolSize: 0,
            //   lineStyle: {
            //     width: 2,
            //     color: paramsName === '已解决' ? '#F3AB29' : '#44C4FF',
            //     type: 'solid'
            //   },
            //   data: [
            //     [
            //       { x: '30%', y: '16%' },
            //       { x: '71%', y: '21%' }
            //     ],
            //     [
            //       { x: '30%', y: '84%' },
            //       { x: '72%', y: '79%' }
            //     ]
            //   ]
            // }
          }
        ]
      }
      if (this.myChart) {
        this.myChart.setOption(this.option, true)
      }
    }

  }
}
</script>

<style lang="scss" scoped>
@media (min-width: 2500px) {
  .barbox {
    width: calc(100% - 40px) !important;
    padding: 0 20px;
  }
}
.barbox {
  height: 100%;
  width: 100%;
  position: relative;
  >div{
    width: 100%;
    height: 100%;
  }
  .legend{
    position: absolute;
    top: 0px;
    width: 150px;
    height: calc(100% - 40px);
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    left: 55%;
    font-size: 15px;
    padding: 10px 0 30px;
    span{
      color: #f4af1b;
      font-weight: 600;
    }
    .legend-item{
      padding-bottom: 4px;
      text-align: center;
      position: relative;
      &::after {
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        position: absolute;
        left: -6px;
        top: calc(50% + 10px);
      }
      &.legend-item-1 {
        border-bottom: 1px solid #6CC6FF;
        &::after {
          background-color: #6CC6FF;
        }
      }
      &.legend-item-2 {
       border-bottom: 1px solid #67A1FF;
        &::after {
          background-color: #67A1FF;
        }
      }
      &.legend-item-3 {
        border-bottom: 1px solid #F7BB7A;
        &::after {
          background-color: #F7BB7A;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.p-c-class {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 4px;
  border-radius: 10px;
}
</style>
